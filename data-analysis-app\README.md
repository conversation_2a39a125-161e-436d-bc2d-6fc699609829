# Interactive Data Analysis Dashboard

A powerful data analysis application that accepts different file formats, creates interactive dashboards with insights, and has online publishing capabilities.

## Features

- **Multi-format Data Import**: Upload and analyze data from CSV, Excel, JSON, and text files
- **Interactive Dashboards**: Create engaging visualizations with <PERSON>lotly and Streamlit
- **Automated Insights**: Get AI-powered insights and statistical analysis of your data
- **Dashboard Publishing**: Share your dashboards online with customizable access controls
- **Data Processing**: Clean, transform, and analyze your data with powerful tools
- **Collaboration**: Work together on dashboards with team members

## Technology Stack

- **Frontend**: Streamlit for interactive web interface
- **Backend**: FastAPI for high-performance API endpoints
- **Data Processing**: Pandas, NumPy, and scikit-learn
- **Visualization**: Plotly and Matplotlib
- **Storage**: Local file system with SQLite (expandable to PostgreSQL)

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/data-analysis-app.git
   cd data-analysis-app
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

1. Start the application:
   ```
   python run.py
   ```

2. Open your browser and navigate to:
   - Frontend Dashboard: http://localhost:8501
   - Backend API: http://localhost:8000

3. Upload your data file using the "Upload Data" page

4. Explore your data and create visualizations in the "Dashboard Builder"

5. View automated insights in the "Insights" page

6. Publish and share your dashboard using the "Publish Dashboard" page

## Application Structure

```
data-analysis-app/
├── app/
│   ├── api/            # FastAPI backend
│   ├── frontend/       # Streamlit frontend
│   ├── core/           # Core application logic
│   ├── data/           # Data storage
│   │   ├── uploads/    # Uploaded files
│   │   └── processed/  # Processed data files
│   ├── utils/          # Utility functions
│   └── models/         # Data models
├── requirements.txt    # Python dependencies
├── run.py              # Application entry point
└── README.md           # Documentation
```

## Dashboard Features

### Data Import
- Support for CSV, Excel, JSON, and text files
- Data preview and validation
- Schema detection and column type inference

### Data Explorer
- View and filter your data
- Examine data statistics and distributions
- Identify missing values and outliers

### Dashboard Builder
- Create various chart types:
  - Bar charts
  - Line charts
  - Scatter plots
  - Histograms
  - Box plots
  - Pie charts
  - Heatmaps
- Customize colors, themes, and layouts
- Add interactive filters and controls

### Automated Insights
- Statistical summaries
- Correlation analysis
- Distribution analysis
- Outlier detection
- Trend identification

### Publishing
- Export to various formats (HTML, PDF, PNG)
- Share via unique URLs
- Control access permissions
- Embed in websites

## Additional Features

- **Automated Insights**: AI-powered data analysis suggestions
- **Collaboration Tools**: Real-time collaboration on dashboards
- **Scheduled Reports**: Automated report generation
- **Mobile Responsiveness**: Adaptive dashboard layouts
- **Data Connectors**: Integration with popular data sources
- **Custom Branding**: White-labeling options

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Streamlit](https://streamlit.io/)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Pandas](https://pandas.pydata.org/)
- [Plotly](https://plotly.com/)
