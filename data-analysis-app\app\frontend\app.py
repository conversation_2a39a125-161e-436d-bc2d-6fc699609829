import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import requests
import json
import os
import io
from datetime import datetime

# Set page configuration
st.set_page_config(
    page_title="Data Analysis Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API endpoint (FastAPI backend)
API_URL = "http://localhost:8000"

# App title and description
st.title("📊 Interactive Data Analysis Dashboard")
st.markdown("""
This application allows you to upload data files, analyze them, and create interactive dashboards.
Upload your data file (CSV, Excel, JSON) to get started!
""")

# Sidebar for navigation
st.sidebar.title("Navigation")
page = st.sidebar.radio("Go to", ["Upload Data", "Data Explorer", "Dashboard Builder", "Insights", "Publish Dashboard"])

# Function to upload file to API
def upload_file(file):
    files = {"file": (file.name, file.getvalue(), file.type)}
    response = requests.post(f"{API_URL}/upload/", files=files)
    return response.json()

# Function to get file list from API
def get_file_list():
    try:
        response = requests.get(f"{API_URL}/files/")
        return response.json()
    except:
        st.error("Could not connect to the API. Make sure the backend server is running.")
        return []

# Function to get data preview from API
def get_data_preview(file_id, limit=1000):
    response = requests.get(f"{API_URL}/data/{file_id}?limit={limit}")
    return response.json()

# Function to get statistics from API
def get_statistics(file_id):
    response = requests.get(f"{API_URL}/stats/{file_id}")
    return response.json()

# Upload Data Page
if page == "Upload Data":
    st.header("Upload Your Data")
    
    uploaded_file = st.file_uploader("Choose a file", type=["csv", "xlsx", "xls", "json", "txt"])
    
    if uploaded_file is not None:
        with st.spinner("Uploading and processing file..."):
            try:
                file_info = upload_file(uploaded_file)
                st.success(f"File uploaded successfully: {uploaded_file.name}")
                
                # Display file information
                st.subheader("File Information")
                st.write(f"Rows: {file_info['rows']}")
                st.write(f"Columns: {file_info['columns']}")
                
                # Display column names and types
                st.subheader("Column Information")
                col_df = pd.DataFrame({
                    "Column Name": file_info["column_names"],
                    "Data Type": [file_info["dtypes"][col] for col in file_info["column_names"]]
                })
                st.table(col_df)
                
                # Store file_id in session state for other pages
                st.session_state["current_file_id"] = file_info["file_id"]
                st.session_state["current_filename"] = uploaded_file.name
                
                st.info("Go to the Data Explorer page to analyze your data.")
            except Exception as e:
                st.error(f"Error uploading file: {str(e)}")

# Data Explorer Page
elif page == "Data Explorer":
    st.header("Data Explorer")
    
    # Get list of uploaded files
    files = get_file_list()
    
    if not files:
        st.info("No files uploaded yet. Please upload a file first.")
    else:
        # File selection
        file_options = {f["filename"]: f["file_id"] for f in files}
        
        # Use current file if available in session state
        default_file = st.session_state.get("current_filename", list(file_options.keys())[0]) if file_options else None
        
        if default_file:
            selected_file = st.selectbox("Select a file to explore", list(file_options.keys()), index=list(file_options.keys()).index(default_file) if default_file in file_options else 0)
            file_id = file_options[selected_file]
            
            # Store selected file in session state
            st.session_state["current_file_id"] = file_id
            st.session_state["current_filename"] = selected_file
            
            # Get data preview
            with st.spinner("Loading data..."):
                data = get_data_preview(file_id)
                df = pd.DataFrame(data)
                
                # Display data preview
                st.subheader("Data Preview")
                st.dataframe(df.head(100))
                
                # Display statistics
                st.subheader("Data Statistics")
                stats = get_statistics(file_id)
                
                # Create tabs for different statistics
                tab1, tab2, tab3 = st.tabs(["Numeric Statistics", "Missing Values", "Categorical Data"])
                
                with tab1:
                    # Display numeric statistics
                    if stats["numeric_stats"]:
                        for col, col_stats in stats["numeric_stats"].items():
                            st.write(f"**{col}**")
                            st.table(pd.DataFrame(col_stats, index=[0]).T)
                    else:
                        st.info("No numeric columns found in the dataset.")
                
                with tab2:
                    # Display null counts
                    null_df = pd.DataFrame({"Column": list(stats["null_counts"].keys()),
                                           "Missing Values": list(stats["null_counts"].values())})
                    null_df["Missing Percentage"] = (null_df["Missing Values"] / len(df) * 100).round(2)
                    st.table(null_df.sort_values("Missing Values", ascending=False))
                
                with tab3:
                    # Display categorical statistics
                    if stats["categorical_stats"]:
                        for col, values in stats["categorical_stats"].items():
                            st.write(f"**{col}**")
                            cat_df = pd.DataFrame({"Value": list(values.keys()),
                                                 "Count": list(values.values())})
                            st.table(cat_df)
                    else:
                        st.info("No categorical columns found in the dataset.")

# Dashboard Builder Page
elif page == "Dashboard Builder":
    st.header("Dashboard Builder")
    
    if "current_file_id" not in st.session_state:
        st.info("Please upload or select a file first.")
    else:
        file_id = st.session_state["current_file_id"]
        
        # Get data
        with st.spinner("Loading data..."):
            data = get_data_preview(file_id, limit=10000)
            df = pd.DataFrame(data)
            
            # Dashboard configuration
            st.subheader("Configure Your Dashboard")
            
            # Layout with columns
            col1, col2 = st.columns([1, 1])
            
            with col1:
                # Chart type selection
                chart_type = st.selectbox(
                    "Select Chart Type",
                    ["Bar Chart", "Line Chart", "Scatter Plot", "Histogram", "Box Plot", "Pie Chart", "Heatmap"]
                )
                
                # Column selection based on chart type
                numeric_cols = df.select_dtypes(include=np.number).columns.tolist()
                categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
                
                if chart_type in ["Bar Chart", "Pie Chart"]:
                    x_col = st.selectbox("Select Category (X-axis)", categorical_cols if categorical_cols else df.columns.tolist())
                    y_col = st.selectbox("Select Value (Y-axis)", numeric_cols if numeric_cols else df.columns.tolist())
                    color_col = st.selectbox("Select Color Variable (optional)", ["None"] + categorical_cols)
                    
                elif chart_type == "Line Chart":
                    x_col = st.selectbox("Select X-axis", df.columns.tolist())
                    y_col = st.selectbox("Select Y-axis", numeric_cols if numeric_cols else df.columns.tolist())
                    color_col = st.selectbox("Select Line Groups (optional)", ["None"] + categorical_cols)
                    
                elif chart_type == "Scatter Plot":
                    x_col = st.selectbox("Select X-axis", numeric_cols if numeric_cols else df.columns.tolist())
                    y_col = st.selectbox("Select Y-axis", numeric_cols if numeric_cols else df.columns.tolist())
                    color_col = st.selectbox("Select Color Variable (optional)", ["None"] + categorical_cols)
                    size_col = st.selectbox("Select Size Variable (optional)", ["None"] + numeric_cols)
                    
                elif chart_type == "Histogram":
                    x_col = st.selectbox("Select Variable", numeric_cols if numeric_cols else df.columns.tolist())
                    y_col = None
                    color_col = st.selectbox("Select Groups (optional)", ["None"] + categorical_cols)
                    
                elif chart_type == "Box Plot":
                    x_col = st.selectbox("Select Category", categorical_cols if categorical_cols else ["None"])
                    y_col = st.selectbox("Select Value", numeric_cols if numeric_cols else df.columns.tolist())
                    color_col = "None"
                    
                elif chart_type == "Heatmap":
                    corr_matrix = df.select_dtypes(include=np.number).corr()
                    x_col = "index"
                    y_col = "columns"
                    color_col = "value"
            
            with col2:
                # Chart title and options
                chart_title = st.text_input("Chart Title", f"{chart_type} of {y_col if y_col else x_col}")
                
                # Additional options based on chart type
                if chart_type in ["Bar Chart", "Line Chart", "Scatter Plot"]:
                    orientation = st.selectbox("Orientation", ["Vertical", "Horizontal"])
                
                if chart_type in ["Bar Chart", "Histogram"]:
                    barmode = st.selectbox("Bar Mode", ["group", "stack", "relative"])
                
                # Theme selection
                theme = st.selectbox("Select Theme", ["plotly", "plotly_white", "plotly_dark", "ggplot2", "seaborn"])
                
                # Generate button
                generate_chart = st.button("Generate Chart")
            
            # Generate and display chart
            if generate_chart:
                st.subheader(chart_title)
                
                with st.spinner("Generating chart..."):
                    try:
                        if chart_type == "Bar Chart":
                            color_param = None if color_col == "None" else color_col
                            if orientation == "Vertical":
                                fig = px.bar(df, x=x_col, y=y_col, color=color_param, title=chart_title, barmode=barmode, template=theme)
                            else:
                                fig = px.bar(df, y=x_col, x=y_col, color=color_param, title=chart_title, barmode=barmode, template=theme, orientation='h')
                        
                        elif chart_type == "Line Chart":
                            color_param = None if color_col == "None" else color_col
                            fig = px.line(df, x=x_col, y=y_col, color=color_param, title=chart_title, template=theme)
                            
                        elif chart_type == "Scatter Plot":
                            color_param = None if color_col == "None" else color_col
                            size_param = None if size_col == "None" else size_col
                            fig = px.scatter(df, x=x_col, y=y_col, color=color_param, size=size_param, title=chart_title, template=theme)
                            
                        elif chart_type == "Histogram":
                            color_param = None if color_col == "None" else color_col
                            fig = px.histogram(df, x=x_col, color=color_param, title=chart_title, barmode=barmode, template=theme)
                            
                        elif chart_type == "Box Plot":
                            x_param = None if x_col == "None" else x_col
                            fig = px.box(df, x=x_param, y=y_col, title=chart_title, template=theme)
                            
                        elif chart_type == "Pie Chart":
                            fig = px.pie(df, names=x_col, values=y_col, title=chart_title, template=theme)
                            
                        elif chart_type == "Heatmap":
                            corr_matrix = df.select_dtypes(include=np.number).corr()
                            fig = px.imshow(corr_matrix, title="Correlation Matrix", template=theme)
                        
                        # Display the chart
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # Save chart to session state for publishing
                        if "dashboard_charts" not in st.session_state:
                            st.session_state["dashboard_charts"] = []
                        
                        # Add chart to dashboard
                        chart_data = {
                            "chart_type": chart_type,
                            "title": chart_title,
                            "config": {
                                "x_col": x_col,
                                "y_col": y_col,
                                "color_col": color_col,
                                "theme": theme
                            },
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        st.session_state["dashboard_charts"].append(chart_data)
                        st.success(f"Chart added to dashboard! Total charts: {len(st.session_state['dashboard_charts'])}")
                        
                    except Exception as e:
                        st.error(f"Error generating chart: {str(e)}")

# Insights Page
elif page == "Insights":
    st.header("Automated Insights")
    
    if "current_file_id" not in st.session_state:
        st.info("Please upload or select a file first.")
    else:
        file_id = st.session_state["current_file_id"]
        
        # Get data
        with st.spinner("Loading data..."):
            data = get_data_preview(file_id, limit=10000)
            df = pd.DataFrame(data)
            
            # Generate insights
            st.subheader("Key Insights from Your Data")
            
            # Basic dataset information
            st.write(f"📊 Your dataset has **{df.shape[0]} rows** and **{df.shape[1]} columns**.")
            
            # Missing values analysis
            missing_values = df.isnull().sum()
            missing_percent = (missing_values / len(df) * 100).round(2)
            
            if missing_values.sum() > 0:
                st.write("⚠️ **Missing Values Analysis**")
                missing_df = pd.DataFrame({
                    "Missing Values": missing_values,
                    "Percentage": missing_percent
                }).sort_values("Missing Values", ascending=False)
                
                st.table(missing_df[missing_df["Missing Values"] > 0])
            else:
                st.write("✅ Your dataset has no missing values.")
            
            # Numeric columns analysis
            numeric_cols = df.select_dtypes(include=np.number).columns.tolist()
            
            if numeric_cols:
                st.write("📈 **Numeric Columns Analysis**")
                
                # Summary statistics
                numeric_summary = df[numeric_cols].describe().T
                st.table(numeric_summary[["count", "mean", "min", "max", "std"]])
                
                # Correlation analysis
                if len(numeric_cols) > 1:
                    st.write("🔄 **Correlation Analysis**")
                    corr_matrix = df[numeric_cols].corr()
                    
                    # Find highest correlations
                    corr_pairs = []
                    for i in range(len(numeric_cols)):
                        for j in range(i+1, len(numeric_cols)):
                            corr_pairs.append({
                                "Variable 1": numeric_cols[i],
                                "Variable 2": numeric_cols[j],
                                "Correlation": corr_matrix.iloc[i, j]
                            })
                    
                    if corr_pairs:
                        corr_df = pd.DataFrame(corr_pairs).sort_values("Correlation", ascending=False, key=abs)
                        st.table(corr_df.head(5))
                        
                        # Visualize top correlation
                        if len(corr_df) > 0:
                            top_pair = corr_df.iloc[0]
                            var1, var2 = top_pair["Variable 1"], top_pair["Variable 2"]
                            corr_val = top_pair["Correlation"]
                            
                            st.write(f"Strongest correlation: **{var1}** and **{var2}** (r = {corr_val:.2f})")
                            
                            fig = px.scatter(df, x=var1, y=var2, title=f"Correlation between {var1} and {var2}")
                            st.plotly_chart(fig, use_container_width=True)
            
            # Categorical columns analysis
            cat_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            
            if cat_cols:
                st.write("📊 **Categorical Columns Analysis**")
                
                for col in cat_cols[:3]:  # Limit to first 3 categorical columns
                    value_counts = df[col].value_counts().head(5)
                    st.write(f"Top values for **{col}**:")
                    
                    fig = px.bar(
                        x=value_counts.index, 
                        y=value_counts.values,
                        title=f"Top values for {col}",
                        labels={"x": col, "y": "Count"}
                    )
                    st.plotly_chart(fig, use_container_width=True)

# Publish Dashboard Page
elif page == "Publish Dashboard":
    st.header("Publish Your Dashboard")
    
    if "dashboard_charts" not in st.session_state or not st.session_state["dashboard_charts"]:
        st.info("You haven't created any charts yet. Go to the Dashboard Builder to create charts.")
    else:
        # Dashboard title and description
        dashboard_title = st.text_input("Dashboard Title", "My Interactive Dashboard")
        dashboard_description = st.text_area("Dashboard Description", "This dashboard provides insights into my data.")
        
        # Display all charts in the dashboard
        st.subheader("Dashboard Preview")
        st.write(f"**{dashboard_title}**")
        st.write(dashboard_description)
        
        # Create a grid layout for the dashboard
        charts = st.session_state["dashboard_charts"]
        
        # Display charts in a grid (2 columns)
        for i in range(0, len(charts), 2):
            cols = st.columns(2)
            
            for j in range(2):
                if i + j < len(charts):
                    chart = charts[i + j]
                    with cols[j]:
                        st.subheader(chart["title"])
                        st.write(f"Type: {chart['chart_type']}")
                        st.write(f"Created: {chart['timestamp']}")
                        
                        # We would normally regenerate the chart here
                        # For simplicity, we'll just show a placeholder
                        st.info("Chart visualization would appear here in the published dashboard")
        
        # Publishing options
        st.subheader("Publishing Options")
        
        col1, col2 = st.columns(2)
        
        with col1:
            publish_format = st.selectbox("Export Format", ["Interactive HTML", "PDF Report", "Image (PNG)"])
            
        with col2:
            sharing_option = st.selectbox("Sharing Options", ["Public URL", "Password Protected", "Private (Email Only)"])
        
        # Publish button
        if st.button("Publish Dashboard"):
            st.success("Dashboard published successfully!")
            
            # Generate a mock URL for the published dashboard
            mock_url = f"https://data-analysis-app.example.com/dashboard/{hash(dashboard_title) % 10000}"
            
            st.code(mock_url)
            
            st.info("In a production environment, this would generate a shareable link to your dashboard.")
            
            # Download options
            st.download_button(
                label=f"Download as {publish_format.split()[0]}",
                data="Dashboard data would be here",
                file_name=f"{dashboard_title.replace(' ', '_')}.html",
                mime="text/html"
            )

# Add a footer
st.markdown("---")
st.markdown("© 2023 Data Analysis Dashboard App | Created with Streamlit and FastAPI")
