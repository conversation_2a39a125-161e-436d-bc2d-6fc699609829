from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np
import json
import os
from typing import List, Optional
import uuid
from datetime import datetime

app = FastAPI(title="Data Analysis API", 
              description="API for data analysis and dashboard generation",
              version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create data directory if it doesn't exist
os.makedirs("../data/uploads", exist_ok=True)
os.makedirs("../data/processed", exist_ok=True)

@app.get("/")
async def root():
    return {"message": "Welcome to the Data Analysis API"}

@app.post("/upload/")
async def upload_file(file: UploadFile = File(...)):
    """
    Upload a data file for analysis.
    Supports CSV, Excel, JSON, and text files.
    """
    # Generate a unique filename
    file_extension = os.path.splitext(file.filename)[1].lower()
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = f"../data/uploads/{unique_filename}"
    
    # Save the uploaded file
    with open(file_path, "wb") as buffer:
        buffer.write(await file.read())
    
    # Process the file based on its extension
    try:
        if file_extension in ['.csv', '.txt']:
            df = pd.read_csv(file_path)
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_extension == '.json':
            df = pd.read_json(file_path)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file format: {file_extension}")
        
        # Save processed data
        processed_path = f"../data/processed/{unique_filename}.parquet"
        df.to_parquet(processed_path)
        
        # Generate basic statistics
        stats = {
            "filename": file.filename,
            "rows": len(df),
            "columns": len(df.columns),
            "column_names": df.columns.tolist(),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "file_id": unique_filename,
            "upload_time": datetime.now().isoformat()
        }
        
        return JSONResponse(content=stats)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@app.get("/files/")
async def list_files():
    """List all uploaded files"""
    try:
        files = []
        for filename in os.listdir("../data/processed"):
            if filename.endswith(".parquet"):
                file_id = os.path.splitext(filename)[0]
                files.append({
                    "file_id": file_id,
                    "filename": filename
                })
        return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing files: {str(e)}")

@app.get("/data/{file_id}")
async def get_data(file_id: str, limit: int = 100):
    """Get data preview for a specific file"""
    try:
        file_path = f"../data/processed/{file_id}.parquet"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {file_id}")
        
        df = pd.read_parquet(file_path)
        return df.head(limit).to_dict(orient="records")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving data: {str(e)}")

@app.get("/stats/{file_id}")
async def get_stats(file_id: str):
    """Get statistical summary for a specific file"""
    try:
        file_path = f"../data/processed/{file_id}.parquet"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {file_id}")
        
        df = pd.read_parquet(file_path)
        
        # Generate descriptive statistics
        numeric_stats = df.describe().to_dict()
        
        # Count null values
        null_counts = df.isnull().sum().to_dict()
        
        # Get unique value counts for categorical columns
        categorical_stats = {}
        for col in df.select_dtypes(include=['object', 'category']).columns:
            categorical_stats[col] = df[col].value_counts().head(10).to_dict()
        
        return {
            "numeric_stats": numeric_stats,
            "null_counts": null_counts,
            "categorical_stats": categorical_stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating statistics: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
