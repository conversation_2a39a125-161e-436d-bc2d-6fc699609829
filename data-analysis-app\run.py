import os
import subprocess
import sys
import time
import threading
import webbrowser

def start_backend():
    """Start the FastAPI backend server"""
    print("Starting FastAPI backend server...")
    os.chdir("app/api")
    subprocess.Popen([sys.executable, "main.py"])
    os.chdir("../..")
    print("Backend server started!")

def start_frontend():
    """Start the Streamlit frontend server"""
    print("Starting Streamlit frontend server...")
    os.chdir("app/frontend")
    subprocess.Popen([sys.executable, "-m", "streamlit", "run", "app.py"])
    os.chdir("../..")
    print("Frontend server started!")

def open_browser():
    """Open the browser after a delay to ensure servers are running"""
    time.sleep(5)  # Wait for servers to start
    print("Opening application in browser...")
    webbrowser.open("http://localhost:8501")  # Streamlit default port

if __name__ == "__main__":
    # Create necessary directories
    os.makedirs("app/data/uploads", exist_ok=True)
    os.makedirs("app/data/processed", exist_ok=True)
    
    # Start backend server in a separate thread
    backend_thread = threading.Thread(target=start_backend)
    backend_thread.daemon = True
    backend_thread.start()
    
    # Start frontend server in a separate thread
    frontend_thread = threading.Thread(target=start_frontend)
    frontend_thread.daemon = True
    frontend_thread.start()
    
    # Open browser after a delay
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("Data Analysis Application is running!")
    print("Backend API: http://localhost:8000")
    print("Frontend Dashboard: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down the application...")
        sys.exit(0)
