import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import json
import os
from typing import Dict, List, Union, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Utility class for data processing and analysis
    """
    
    def __init__(self, df: pd.DataFrame = None, file_path: str = None):
        """
        Initialize with either a DataFrame or a file path
        """
        if df is not None:
            self.df = df
        elif file_path is not None:
            self.load_data(file_path)
        else:
            self.df = None
            
        self.original_df = self.df.copy() if self.df is not None else None
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        Load data from various file formats
        """
        try:
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension in ['.csv', '.txt']:
                self.df = pd.read_csv(file_path)
            elif file_extension in ['.xlsx', '.xls']:
                self.df = pd.read_excel(file_path)
            elif file_extension == '.json':
                self.df = pd.read_json(file_path)
            elif file_extension == '.parquet':
                self.df = pd.read_parquet(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_extension}")
                
            self.original_df = self.df.copy()
            return self.df
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def get_basic_stats(self) -> Dict:
        """
        Get basic statistics about the dataset
        """
        if self.df is None:
            return {"error": "No data loaded"}
        
        try:
            stats = {
                "rows": len(self.df),
                "columns": len(self.df.columns),
                "column_names": self.df.columns.tolist(),
                "dtypes": {col: str(dtype) for col, dtype in self.df.dtypes.items()},
                "missing_values": self.df.isnull().sum().to_dict(),
                "missing_percentage": (self.df.isnull().sum() / len(self.df) * 100).round(2).to_dict()
            }
            
            # Add numeric column statistics
            numeric_cols = self.df.select_dtypes(include=np.number).columns.tolist()
            if numeric_cols:
                stats["numeric_stats"] = self.df[numeric_cols].describe().to_dict()
            
            # Add categorical column statistics
            cat_cols = self.df.select_dtypes(include=['object', 'category']).columns.tolist()
            if cat_cols:
                stats["categorical_stats"] = {}
                for col in cat_cols:
                    stats["categorical_stats"][col] = self.df[col].value_counts().head(10).to_dict()
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {str(e)}")
            return {"error": str(e)}
    
    def clean_data(self, 
                  drop_na: bool = False, 
                  fill_na: Optional[Dict] = None,
                  drop_duplicates: bool = False,
                  drop_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Clean the dataset with various options
        """
        if self.df is None:
            raise ValueError("No data loaded")
        
        try:
            # Create a copy to avoid modifying the original
            cleaned_df = self.df.copy()
            
            # Drop specified columns
            if drop_columns:
                cleaned_df = cleaned_df.drop(columns=[col for col in drop_columns if col in cleaned_df.columns])
            
            # Handle missing values
            if drop_na:
                cleaned_df = cleaned_df.dropna()
            elif fill_na:
                for col, value in fill_na.items():
                    if col in cleaned_df.columns:
                        cleaned_df[col] = cleaned_df[col].fillna(value)
            
            # Drop duplicates
            if drop_duplicates:
                cleaned_df = cleaned_df.drop_duplicates()
            
            self.df = cleaned_df
            return self.df
            
        except Exception as e:
            logger.error(f"Error cleaning data: {str(e)}")
            raise
    
    def normalize_data(self, 
                      columns: List[str], 
                      method: str = 'standard') -> pd.DataFrame:
        """
        Normalize numeric columns using different methods
        """
        if self.df is None:
            raise ValueError("No data loaded")
        
        try:
            # Filter columns that exist in the dataframe
            valid_columns = [col for col in columns if col in self.df.columns]
            
            if not valid_columns:
                raise ValueError("No valid columns specified for normalization")
            
            # Create a copy to avoid modifying the original
            normalized_df = self.df.copy()
            
            if method == 'standard':
                scaler = StandardScaler()
                normalized_df[valid_columns] = scaler.fit_transform(normalized_df[valid_columns])
            elif method == 'minmax':
                scaler = MinMaxScaler()
                normalized_df[valid_columns] = scaler.fit_transform(normalized_df[valid_columns])
            else:
                raise ValueError(f"Unsupported normalization method: {method}")
            
            self.df = normalized_df
            return self.df
            
        except Exception as e:
            logger.error(f"Error normalizing data: {str(e)}")
            raise
    
    def generate_insights(self) -> Dict:
        """
        Generate automated insights from the data
        """
        if self.df is None:
            return {"error": "No data loaded"}
        
        try:
            insights = {
                "basic_info": {
                    "rows": len(self.df),
                    "columns": len(self.df.columns),
                    "memory_usage": self.df.memory_usage(deep=True).sum() / (1024 * 1024),  # in MB
                },
                "data_quality": {},
                "correlations": {},
                "distributions": {},
                "outliers": {}
            }
            
            # Data quality insights
            missing_values = self.df.isnull().sum()
            insights["data_quality"]["missing_values"] = missing_values[missing_values > 0].to_dict()
            insights["data_quality"]["missing_percentage"] = (missing_values / len(self.df) * 100).round(2)[missing_values > 0].to_dict()
            
            # Correlation insights for numeric columns
            numeric_cols = self.df.select_dtypes(include=np.number).columns.tolist()
            if len(numeric_cols) > 1:
                corr_matrix = self.df[numeric_cols].corr()
                
                # Find highest absolute correlations
                high_corr_pairs = []
                for i in range(len(numeric_cols)):
                    for j in range(i+1, len(numeric_cols)):
                        corr_value = corr_matrix.iloc[i, j]
                        if abs(corr_value) > 0.5:  # Only include correlations > 0.5
                            high_corr_pairs.append({
                                "var1": numeric_cols[i],
                                "var2": numeric_cols[j],
                                "correlation": corr_value
                            })
                
                insights["correlations"]["high_correlations"] = sorted(high_corr_pairs, key=lambda x: abs(x["correlation"]), reverse=True)
            
            # Distribution insights
            for col in numeric_cols:
                col_stats = self.df[col].describe()
                skewness = self.df[col].skew()
                
                insights["distributions"][col] = {
                    "mean": col_stats["mean"],
                    "median": self.df[col].median(),
                    "std": col_stats["std"],
                    "min": col_stats["min"],
                    "max": col_stats["max"],
                    "skewness": skewness,
                    "distribution_type": "right-skewed" if skewness > 0.5 else "left-skewed" if skewness < -0.5 else "normal"
                }
            
            # Outlier detection
            for col in numeric_cols:
                q1 = self.df[col].quantile(0.25)
                q3 = self.df[col].quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = self.df[(self.df[col] < lower_bound) | (self.df[col] > upper_bound)][col]
                
                if len(outliers) > 0:
                    insights["outliers"][col] = {
                        "count": len(outliers),
                        "percentage": (len(outliers) / len(self.df) * 100).round(2),
                        "min_outlier": float(outliers.min()) if not pd.isna(outliers.min()) else None,
                        "max_outlier": float(outliers.max()) if not pd.isna(outliers.max()) else None
                    }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            return {"error": str(e)}
    
    def perform_clustering(self, 
                          columns: List[str], 
                          n_clusters: int = 3) -> Tuple[pd.DataFrame, Dict]:
        """
        Perform K-means clustering on selected columns
        """
        if self.df is None:
            raise ValueError("No data loaded")
        
        try:
            # Filter columns that exist in the dataframe
            valid_columns = [col for col in columns if col in self.df.columns]
            
            if not valid_columns:
                raise ValueError("No valid columns specified for clustering")
            
            # Create a copy and select only numeric columns from the valid columns
            cluster_df = self.df[valid_columns].select_dtypes(include=np.number).copy()
            
            if cluster_df.empty:
                raise ValueError("No numeric columns available for clustering")
            
            # Standardize the data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(cluster_df)
            
            # Perform K-means clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(scaled_data)
            
            # Add cluster labels to the original dataframe
            result_df = self.df.copy()
            result_df['cluster'] = clusters
            
            # Generate cluster statistics
            cluster_stats = {}
            for i in range(n_clusters):
                cluster_data = result_df[result_df['cluster'] == i]
                cluster_stats[f"Cluster {i}"] = {
                    "size": len(cluster_data),
                    "percentage": (len(cluster_data) / len(result_df) * 100).round(2),
                    "centroid": {col: float(val) for col, val in zip(cluster_df.columns, kmeans.cluster_centers_[i])}
                }
            
            return result_df, cluster_stats
            
        except Exception as e:
            logger.error(f"Error performing clustering: {str(e)}")
            raise
    
    def reset_to_original(self) -> pd.DataFrame:
        """
        Reset the dataframe to its original state
        """
        if self.original_df is None:
            raise ValueError("No original data available")
        
        self.df = self.original_df.copy()
        return self.df
